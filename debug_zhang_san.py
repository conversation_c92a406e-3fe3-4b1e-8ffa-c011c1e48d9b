#!/usr/bin/env python3
"""
调试张三聚类问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text

def debug_zhang_san_clustering():
    """调试张三的聚类问题"""
    engine = create_engine('sqlite:///debug_zhang_san.db', echo=False)
    
    with engine.connect() as conn:
        # 创建表
        conn.execute(text("DROP TABLE IF EXISTS qz_case_feature"))
        conn.execute(text("DROP TABLE IF EXISTS police_records"))
        
        conn.execute(text("""
            CREATE TABLE qz_case_feature (
                case_id TEXT,
                entity_id INTEGER,
                feature_type TEXT,
                feature_value TEXT
            )
        """))
        
        conn.execute(text("""
            CREATE TABLE police_records (
                police_number TEXT PRIMARY KEY,
                source INTEGER,
                police_time DATETIME
            )
        """))
        
        # 插入完整的测试数据，特别关注张三的三个记录
        test_data = [
            # 案件1：张三有身份证
            ("42030020250316T0040206501", 0, "CASE_TYPE", "邻里纠纷"),
            ("42030020250316T0040206501", 0, "LOCATION", "116.407395 39.904211"),
            ("42030020250316T0040206501", 1, "ID_CARD", "******************"),
            ("42030020250316T0040206501", 1, "ENTITY_NAME", "张三"),
            ("42030020250316T0040206501", 1, "ENTITY_TYPE", "报警人"),
            
            # 案件2：张三无身份证
            ("42030320250316235726110001", 0, "CASE_TYPE", "家庭纠纷"),
            ("42030320250316235726110001", 0, "LOCATION", "116.407395 39.904211"),
            ("42030320250316235726110001", 1, "ENTITY_NAME", "张三"),
            ("42030320250316235726110001", 1, "ENTITY_TYPE", "报警人"),
            ("42030320250316235726110001", 2, "ID_CARD", "******************"),
            ("42030320250316235726110001", 2, "ENTITY_NAME", "李四"),
            ("42030320250316235726110001", 2, "ENTITY_TYPE", "受害者"),
            
            # 案件3：张三有身份证（同一个人）
            ("42039920250316222245110001", 0, "CASE_TYPE", "家庭纠纷"),
            ("42039920250316222245110001", 0, "LOCATION", "116.407395 39.904211"),
            ("42039920250316222245110001", 1, "ID_CARD", "******************"),
            ("42039920250316222245110001", 1, "ENTITY_NAME", "张三"),
            ("42039920250316222245110001", 1, "ENTITY_TYPE", "受害者"),
            
            # 其他数据
            ("42030020250316T0080171701", 0, "CASE_TYPE", "斗殴"),
            ("42030020250316T0080171701", 0, "LOCATION", "121.473701 31.230416"),
            ("42030020250316T0080171701", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080171701", 1, "ENTITY_TYPE", "受害者"),
            
            ("42030020250316T0080215701", 0, "CASE_TYPE", "盗窃"),
            ("42030020250316T0080215701", 0, "LOCATION", "116.410000 39.905000"),
            ("42030020250316T0080215701", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080215701", 1, "ENTITY_TYPE", "嫌疑人"),
            ("42030020250316T0080215701", 2, "ID_CARD", "******************"),
            ("42030020250316T0080215701", 2, "ENTITY_NAME", "寄十一"),
            ("42030020250316T0080215701", 2, "ENTITY_TYPE", "嫌疑人"),
            
            ("42030020250316T0080221201", 0, "CASE_TYPE", "盗窃"),
            ("42030020250316T0080221201", 0, "LOCATION", "116.410000 39.905000"),
            ("42030020250316T0080221201", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080221201", 1, "ENTITY_TYPE", "嫌疑人"),
            ("42030020250316T0080221201", 2, "ID_CARD", "******************"),
            ("42030020250316T0080221201", 2, "ENTITY_NAME", "寄十一"),
            ("42030020250316T0080221201", 2, "ENTITY_TYPE", "嫌疑人"),
        ]
        
        for case_id, entity_id, feature_type, feature_value in test_data:
            conn.execute(text("""
                INSERT INTO qz_case_feature (case_id, entity_id, feature_type, feature_value)
                VALUES (:case_id, :entity_id, :feature_type, :feature_value)
            """), {
                'case_id': case_id,
                'entity_id': entity_id,
                'feature_type': feature_type,
                'feature_value': feature_value
            })
        
        # 插入 police_records
        police_data = [
            ("42030020250316T0040206501", "2025-03-16 22:50:31"),
            ("42030320250316235726110001", "2025-03-16 23:56:41"),
            ("42039920250316222245110001", "2025-03-16 22:20:24"),
            ("42030020250316T0080171701", "2025-03-16 22:44:26"),
            ("42030020250316T0080215701", "2025-03-16 22:18:11"),
            ("42030020250316T0080221201", "2025-03-16 23:43:00"),
        ]
        
        for police_number, police_time in police_data:
            conn.execute(text("""
                INSERT INTO police_records (police_number, source, police_time)
                VALUES (:police_number, 0, :police_time)
            """), {
                'police_number': police_number,
                'police_time': police_time
            })
        
        conn.commit()
    
    with engine.connect() as conn:
        print("=== 调试张三聚类问题 ===")
        
        # 1. 查看所有张三的记录
        print("\n1. 所有张三的原始记录:")
        result = conn.execute(text("""
            SELECT case_id, entity_id, feature_type, feature_value
            FROM qz_case_feature
            WHERE feature_value = '张三' OR (feature_type = 'ID_CARD' AND feature_value = '******************')
            ORDER BY case_id, entity_id, feature_type
        """))
        for row in result.fetchall():
            print(f"  {row}")
        
        # 2. 查看pivot_data中的张三
        print("\n2. pivot_data中的张三:")
        result = conn.execute(text("""
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
                  AND qcf.entity_id > 0
                GROUP BY qcf.case_id, qcf.entity_id
            )
            SELECT * FROM pivot_data WHERE ENTITY_NAME = '张三'
            ORDER BY case_id, entity_id
        """))
        for row in result.fetchall():
            print(f"  {row}")
        
        # 3. 查看person_cases中的张三
        print("\n3. person_cases中的张三:")
        result = conn.execute(text("""
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
                  AND qcf.entity_id > 0
                GROUP BY qcf.case_id, qcf.entity_id
            ),
            person_cases AS (
                SELECT
                    ENTITY_NAME,
                    ID_CARD,
                    case_id,
                    entity_id,
                    ENTITY_TYPE,
                    CASE
                        WHEN ID_CARD IS NOT NULL THEN ID_CARD
                        ELSE 'NO_ID_CARD_' || ENTITY_NAME
                    END as cluster_key
                FROM pivot_data
                WHERE ENTITY_NAME IS NOT NULL
            )
            SELECT * FROM person_cases WHERE ENTITY_NAME = '张三'
            ORDER BY case_id, entity_id
        """))
        for row in result.fetchall():
            print(f"  {row}")
        
        # 4. 查看persons_with_multiple_cases中的张三
        print("\n4. persons_with_multiple_cases中的张三:")
        result = conn.execute(text("""
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
                  AND qcf.entity_id > 0
                GROUP BY qcf.case_id, qcf.entity_id
            ),
            person_cases AS (
                SELECT
                    ENTITY_NAME,
                    ID_CARD,
                    case_id,
                    entity_id,
                    ENTITY_TYPE,
                    CASE
                        WHEN ID_CARD IS NOT NULL THEN ID_CARD
                        ELSE 'NO_ID_CARD_' || ENTITY_NAME
                    END as cluster_key
                FROM pivot_data
                WHERE ENTITY_NAME IS NOT NULL
            ),
            persons_with_multiple_cases AS (
                SELECT 
                    cluster_key,
                    ENTITY_NAME,
                    ID_CARD,
                    COUNT(DISTINCT case_id) as case_count
                FROM person_cases
                GROUP BY cluster_key, ENTITY_NAME, ID_CARD
                HAVING COUNT(DISTINCT case_id) >= 2
            )
            SELECT * FROM persons_with_multiple_cases WHERE ENTITY_NAME = '张三'
        """))
        for row in result.fetchall():
            print(f"  {row}")

if __name__ == "__main__":
    debug_zhang_san_clustering()
