#!/usr/bin/env python3
"""
最终集成测试：模拟完整的 cluster_entities 方法调用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from datetime import datetime

def simulate_cluster_entities_method():
    """模拟完整的 cluster_entities 方法"""
    
    # 创建测试数据库
    engine = create_engine('sqlite:///test_final.db', echo=False)
    
    with engine.connect() as conn:
        # 创建表
        conn.execute(text("DROP TABLE IF EXISTS qz_case_feature"))
        conn.execute(text("DROP TABLE IF EXISTS police_records"))
        
        conn.execute(text("""
            CREATE TABLE qz_case_feature (
                case_id TEXT,
                entity_id INTEGER,
                feature_type TEXT,
                feature_value TEXT
            )
        """))
        
        conn.execute(text("""
            CREATE TABLE police_records (
                police_number TEXT PRIMARY KEY,
                source INTEGER,
                police_time DATETIME
            )
        """))
        
        # 插入您的测试数据
        test_data = [
            ("42030020250316T0020221401", 0, "CASE_TYPE", "抢劫"),
            ("42030020250316T0020221401", 0, "LOCATION", "104.066801 30.620555"),
            ("42030020250316T0020221401", 1, "ID_CARD", "510104199106061234"),
            ("42030020250316T0020221401", 1, "ENTITY_NAME", "孙八"),
            ("42030020250316T0020221401", 1, "ENTITY_TYPE", "嫌疑人"),
            
            ("42030020250316T0040206501", 0, "CASE_TYPE", "邻里纠纷"),
            ("42030020250316T0040206501", 0, "LOCATION", "116.407395 39.904211"),
            ("42030020250316T0040206501", 1, "ID_CARD", "******************"),
            ("42030020250316T0040206501", 1, "ENTITY_NAME", "张三"),
            ("42030020250316T0040206501", 1, "ENTITY_TYPE", "报警人"),
            
            ("42030020250316T0080171701", 0, "CASE_TYPE", "斗殴"),
            ("42030020250316T0080171701", 0, "LOCATION", "121.473701 31.230416"),
            ("42030020250316T0080171701", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080171701", 1, "ENTITY_TYPE", "受害者"),
            
            ("42030020250316T0080215701", 0, "CASE_TYPE", "盗窃"),
            ("42030020250316T0080215701", 0, "LOCATION", "116.410000 39.905000"),
            ("42030020250316T0080215701", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080215701", 1, "ENTITY_TYPE", "嫌疑人"),
            ("42030020250316T0080215701", 2, "ID_CARD", "******************"),
            ("42030020250316T0080215701", 2, "ENTITY_NAME", "寄十一"),
            ("42030020250316T0080215701", 2, "ENTITY_TYPE", "嫌疑人"),
            
            ("42030020250316T0080221201", 0, "CASE_TYPE", "盗窃"),
            ("42030020250316T0080221201", 0, "LOCATION", "116.410000 39.905000"),
            ("42030020250316T0080221201", 1, "ENTITY_NAME", "王五"),
            ("42030020250316T0080221201", 1, "ENTITY_TYPE", "嫌疑人"),
            ("42030020250316T0080221201", 2, "ID_CARD", "******************"),
            ("42030020250316T0080221201", 2, "ENTITY_NAME", "寄十一"),
            ("42030020250316T0080221201", 2, "ENTITY_TYPE", "嫌疑人"),
            
            ("42039920250316222245110001", 0, "CASE_TYPE", "家庭纠纷"),
            ("42039920250316222245110001", 0, "LOCATION", "116.407395 39.904211"),
            ("42039920250316222245110001", 1, "ID_CARD", "******************"),
            ("42039920250316222245110001", 1, "ENTITY_NAME", "张三"),
            ("42039920250316222245110001", 1, "ENTITY_TYPE", "受害者"),
        ]
        
        for case_id, entity_id, feature_type, feature_value in test_data:
            conn.execute(text("""
                INSERT INTO qz_case_feature (case_id, entity_id, feature_type, feature_value)
                VALUES (:case_id, :entity_id, :feature_type, :feature_value)
            """), {
                'case_id': case_id,
                'entity_id': entity_id,
                'feature_type': feature_type,
                'feature_value': feature_value
            })
        
        # 插入 police_records
        police_data = [
            ("42030020250316T0020221401", "2025-03-16 23:12:40"),
            ("42030020250316T0040206501", "2025-03-16 22:50:31"),
            ("42030020250316T0080171701", "2025-03-16 22:44:26"),
            ("42030020250316T0080215701", "2025-03-16 22:18:11"),
            ("42030020250316T0080221201", "2025-03-16 23:43:00"),
            ("42039920250316222245110001", "2025-03-16 22:20:24"),
        ]
        
        for police_number, police_time in police_data:
            conn.execute(text("""
                INSERT INTO police_records (police_number, source, police_time)
                VALUES (:police_number, 0, :police_time)
            """), {
                'police_number': police_number,
                'police_time': police_time
            })
        
        conn.commit()
    
    # 模拟方法参数
    page = 1
    page_size = 10
    start_time = None
    end_time = None
    
    print("=== 模拟 cluster_entities 方法调用 ===")
    print(f"参数: page={page}, page_size={page_size}, start_time={start_time}, end_time={end_time}")
    
    # 计算分页参数
    offset = (page - 1) * page_size
    
    # 构建时间过滤条件
    time_filter_sql = ""
    params = {
        'limit': page_size,
        'offset': offset
    }
    
    if start_time:
        try:
            datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            time_filter_sql += " AND pr.police_time >= :start_time"
            params['start_time'] = start_time
        except ValueError:
            print(f"开始时间格式错误: {start_time}")
            return
    
    if end_time:
        try:
            datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            time_filter_sql += " AND pr.police_time <= :end_time"
            params['end_time'] = end_time
        except ValueError:
            print(f"结束时间格式错误: {end_time}")
            return

    # 执行SQL查询
    main_query_sql = f"""
    WITH pivot_data AS (
        SELECT
            qcf.case_id,
            qcf.entity_id,
            MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
        FROM qz_case_feature qcf
        JOIN police_records pr ON qcf.case_id = pr.police_number
        WHERE pr.source = 0 {time_filter_sql}
          AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
          AND qcf.entity_id > 0
        GROUP BY qcf.case_id, qcf.entity_id
    ),
    person_cases AS (
        SELECT
            ENTITY_NAME,
            ID_CARD,
            case_id,
            entity_id,
            ENTITY_TYPE,
            CASE
                WHEN ID_CARD IS NOT NULL THEN ID_CARD
                ELSE 'NO_ID_CARD_' || ENTITY_NAME
            END as cluster_key
        FROM pivot_data
        WHERE ENTITY_NAME IS NOT NULL
    ),
    persons_with_multiple_cases AS (
        SELECT 
            cluster_key,
            ENTITY_NAME,
            ID_CARD,
            COUNT(DISTINCT case_id) as case_count
        FROM person_cases
        GROUP BY cluster_key, ENTITY_NAME, ID_CARD
        HAVING COUNT(DISTINCT case_id) >= 2
    ),
    entity_clusters AS (
        SELECT
            pc.cluster_key,
            pc.case_id,
            pc.entity_id,
            pc.ID_CARD,
            pc.ENTITY_NAME,
            pc.ENTITY_TYPE,
            (SELECT MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END)
             FROM qz_case_feature qcf2
             WHERE qcf2.case_id = pc.case_id AND qcf2.entity_id = 0) AS CASE_TYPE,
            (SELECT MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END)
             FROM qz_case_feature qcf3
             WHERE qcf3.case_id = pc.case_id AND qcf3.entity_id = 0) AS LOCATION,
            pr.police_time
        FROM person_cases pc
        JOIN persons_with_multiple_cases pwmc ON pc.cluster_key = pwmc.cluster_key
        JOIN police_records pr ON pc.case_id = pr.police_number
        WHERE pr.source = 0 {time_filter_sql}
    ),
    cluster_summary AS (
        SELECT
            cluster_key,
            MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
            MAX(ENTITY_NAME) as representative_name,
            COUNT(DISTINCT case_id) as case_count,
            GROUP_CONCAT(
                case_id || '|' || COALESCE(ENTITY_TYPE, 'null') || '|' ||
                COALESCE(strftime('%Y-%m-%d %H:%M:%S', police_time), '') || '|' ||
                COALESCE(CASE_TYPE, '') || '|' ||
                COALESCE(LOCATION, ''), ';;'
            ) as cases_info
        FROM entity_clusters
        GROUP BY cluster_key
        HAVING case_count >= 2
    )
    SELECT * FROM cluster_summary
    ORDER BY case_count DESC
    LIMIT :limit OFFSET :offset
    """
    
    with engine.connect() as conn:
        print("执行SQL查询...")
        result = conn.execute(text(main_query_sql), params)
        clusters = result.fetchall()
        
        print(f"SQL查询返回 {len(clusters)} 个聚类")
        
        if not clusters:
            print("未找到符合条件的实体聚类")
            return {
                "total": 0,
                "items": [],
                "page": page,
                "page_size": page_size
            }
        
        # 解析查询结果并构建返回数据
        all_clusters = []
        for cluster in clusters:
            representative_id_card = cluster[1]
            representative_name = cluster[2]
            cases_info = cluster[4]
            
            # 解析案件信息字符串
            cases_list = []
            if cases_info:
                case_items = cases_info.split(';;')
                for case_item in case_items:
                    parts = case_item.split('|')
                    if len(parts) >= 5:
                        case_id = parts[0]
                        entity_type = parts[1] if parts[1] != 'null' else None
                        police_time = parts[2] if parts[2] else None
                        case_type = parts[3] if parts[3] else None
                        location = parts[4] if parts[4] else None
                        
                        cases_list.append({
                            "role": [entity_type] if entity_type else [],
                            "case": case_id,
                            "police_time": police_time,
                            "case_type": case_type,
                            "location": location
                        })
            
            # 确保至少有两个案件
            if len(cases_list) >= 2:
                all_clusters.append({
                    "name": representative_name,
                    "id_card": representative_id_card,
                    "cases": cases_list
                })
                print(f"添加聚类结果：{representative_name}({representative_id_card})，{len(cases_list)}个案件")
        
        print(f"构建聚类结果完成，共{len(all_clusters)}个符合条件的实体聚类")
        
        # 计算案件总数
        total_cases = sum(len(cluster['cases']) for cluster in all_clusters)
        
        result = {
            "total": total_cases,
            "items": all_clusters,
            "page": page,
            "page_size": page_size
        }
        
        print(f"最终结果：")
        print(f"  total: {result['total']}")
        print(f"  items: {len(result['items'])} 个聚类")
        print(f"  page: {result['page']}")
        print(f"  page_size: {result['page_size']}")
        
        # 详细输出每个聚类
        for i, item in enumerate(result['items'], 1):
            print(f"\n聚类 {i}: {item['name']} (身份证: {item['id_card']})")
            print(f"  案件数量: {len(item['cases'])}")
            for j, case in enumerate(item['cases'], 1):
                print(f"    {j}. {case['case']} - {case['case_type']} - {case['role']} - {case['police_time']}")
        
        return result

if __name__ == "__main__":
    print("开始最终集成测试...")
    simulate_cluster_entities_method()
