#!/usr/bin/env python3
"""
测试 cluster_entities 方法的 SQL 逻辑
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pandas as pd

# 测试数据
test_data = [
    ("42030020250316T0020221401", 0, "CASE_TYPE", "抢劫"),
    ("42030020250316T0020221401", 0, "LOCATION", "104.066801 30.620555"),
    ("42030020250316T0020221401", 1, "ID_CARD", "510104199106061234"),
    ("42030020250316T0020221401", 1, "ENTITY_NAME", "孙八"),
    ("42030020250316T0020221401", 1, "ENTITY_TYPE", "嫌疑人"),

    ("42030020250316T0040206501", 0, "CASE_TYPE", "邻里纠纷"),
    ("42030020250316T0040206501", 0, "LOCATION", "116.407395 39.904211"),
    ("42030020250316T0040206501", 1, "ID_CARD", "******************"),
    ("42030020250316T0040206501", 1, "ENTITY_NAME", "张三"),
    ("42030020250316T0040206501", 1, "ENTITY_TYPE", "报警人"),

    ("42030020250316T0080171701", 0, "CASE_TYPE", "斗殴"),
    ("42030020250316T0080171701", 0, "LOCATION", "121.473701 31.230416"),
    ("42030020250316T0080171701", 1, "ENTITY_NAME", "王五"),
    ("42030020250316T0080171701", 1, "ENTITY_TYPE", "受害者"),

    ("42030020250316T0080215701", 0, "CASE_TYPE", "盗窃"),
    ("42030020250316T0080215701", 0, "LOCATION", "116.410000 39.905000"),
    ("42030020250316T0080215701", 1, "ENTITY_NAME", "王五"),
    ("42030020250316T0080215701", 1, "ENTITY_TYPE", "嫌疑人"),
    ("42030020250316T0080215701", 2, "ID_CARD", "******************"),
    ("42030020250316T0080215701", 2, "ENTITY_NAME", "寄十一"),
    ("42030020250316T0080215701", 2, "ENTITY_TYPE", "嫌疑人"),

    ("42030020250316T0080221201", 0, "CASE_TYPE", "盗窃"),
    ("42030020250316T0080221201", 0, "LOCATION", "116.410000 39.905000"),
    ("42030020250316T0080221201", 1, "ENTITY_NAME", "王五"),
    ("42030020250316T0080221201", 1, "ENTITY_TYPE", "嫌疑人"),
    ("42030020250316T0080221201", 2, "ID_CARD", "******************"),
    ("42030020250316T0080221201", 2, "ENTITY_NAME", "寄十一"),
    ("42030020250316T0080221201", 2, "ENTITY_TYPE", "嫌疑人"),

    ("42039920250316222245110001", 0, "CASE_TYPE", "家庭纠纷"),
    ("42039920250316222245110001", 0, "LOCATION", "116.407395 39.904211"),
    ("42039920250316222245110001", 1, "ID_CARD", "******************"),
    ("42039920250316222245110001", 1, "ENTITY_NAME", "张三"),
    ("42039920250316222245110001", 1, "ENTITY_TYPE", "受害者"),
]

def create_test_database():
    """创建测试数据库和表"""
    engine = create_engine('sqlite:///test_cluster.db', echo=False)

    # 创建测试表
    with engine.connect() as conn:
        # 删除已存在的表
        conn.execute(text("DROP TABLE IF EXISTS qz_case_feature"))
        conn.execute(text("DROP TABLE IF EXISTS police_records"))

        # 创建 qz_case_feature 表
        conn.execute(text("""
            CREATE TABLE qz_case_feature (
                case_id TEXT,
                entity_id INTEGER,
                feature_type TEXT,
                feature_value TEXT
            )
        """))

        # 创建 police_records 表
        conn.execute(text("""
            CREATE TABLE police_records (
                police_number TEXT PRIMARY KEY,
                source INTEGER,
                police_time DATETIME
            )
        """))

        # 插入测试数据到 qz_case_feature
        for case_id, entity_id, feature_type, feature_value in test_data:
            conn.execute(text("""
                INSERT INTO qz_case_feature (case_id, entity_id, feature_type, feature_value)
                VALUES (:case_id, :entity_id, :feature_type, :feature_value)
            """), {
                'case_id': case_id,
                'entity_id': entity_id,
                'feature_type': feature_type,
                'feature_value': feature_value
            })

        # 插入测试数据到 police_records
        case_ids = list(set(item[0] for item in test_data))
        for i, case_id in enumerate(case_ids):
            conn.execute(text("""
                INSERT INTO police_records (police_number, source, police_time)
                VALUES (:police_number, 0, datetime('2025-03-16 22:00:00', '+' || :minutes || ' minutes'))
            """), {
                'police_number': case_id,
                'minutes': i * 10
            })

        conn.commit()

    return engine

def test_current_sql():
    """测试当前的 SQL 逻辑"""
    engine = create_test_database()

    # 修复后的 SQL 查询
    main_query_sql = """
    WITH pivot_data AS (
        SELECT
            qcf.case_id,
            qcf.entity_id,
            MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
        FROM qz_case_feature qcf
        JOIN police_records pr ON qcf.case_id = pr.police_number
        WHERE pr.source = 0
          AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
          AND qcf.entity_id > 0
        GROUP BY qcf.case_id, qcf.entity_id
    ),
    -- 关键修复：按姓名+身份证组合进行聚类，而不是按entity_id
    person_cases AS (
        SELECT
            ENTITY_NAME,
            ID_CARD,
            case_id,
            entity_id,
            ENTITY_TYPE,
            -- 生成聚类键：优先使用身份证，无身份证时使用姓名
            CASE
                WHEN ID_CARD IS NOT NULL THEN ID_CARD
                ELSE 'NO_ID_CARD_' || ENTITY_NAME
            END as cluster_key
        FROM pivot_data
        WHERE ENTITY_NAME IS NOT NULL
    ),
    -- 找到至少出现在两个案件中的人员（按聚类键分组）
    persons_with_multiple_cases AS (
        SELECT
            cluster_key,
            ENTITY_NAME,
            ID_CARD,
            COUNT(DISTINCT case_id) as case_count
        FROM person_cases
        GROUP BY cluster_key, ENTITY_NAME, ID_CARD
        HAVING COUNT(DISTINCT case_id) >= 2
    ),
    -- 获取这些人员的所有案件详情
    entity_clusters AS (
        SELECT
            pc.cluster_key,
            pc.case_id,
            pc.entity_id,
            pc.ID_CARD,
            pc.ENTITY_NAME,
            pc.ENTITY_TYPE,
            (SELECT MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END)
             FROM qz_case_feature qcf2
             WHERE qcf2.case_id = pc.case_id AND qcf2.entity_id = 0) AS CASE_TYPE,
            (SELECT MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END)
             FROM qz_case_feature qcf3
             WHERE qcf3.case_id = pc.case_id AND qcf3.entity_id = 0) AS LOCATION,
            pr.police_time
        FROM person_cases pc
        JOIN persons_with_multiple_cases pwmc ON pc.cluster_key = pwmc.cluster_key
        JOIN police_records pr ON pc.case_id = pr.police_number
        WHERE pr.source = 0
    ),
    cluster_summary AS (
        SELECT
            cluster_key,
            MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
            MAX(ENTITY_NAME) as representative_name,
            COUNT(DISTINCT case_id) as case_count,
            GROUP_CONCAT(
                case_id || '|' || COALESCE(ENTITY_TYPE, 'null') || '|' ||
                COALESCE(datetime(police_time), '') || '|' ||
                COALESCE(CASE_TYPE, '') || '|' ||
                COALESCE(LOCATION, ''), ';;'
            ) as cases_info
        FROM entity_clusters
        GROUP BY cluster_key
        HAVING case_count >= 2
    )
    SELECT * FROM cluster_summary
    ORDER BY case_count DESC
    """

    with engine.connect() as conn:
        print("=== 测试当前 SQL 逻辑 ===")
        result = conn.execute(text(main_query_sql))
        clusters = result.fetchall()

        print(f"找到 {len(clusters)} 个聚类:")
        for cluster in clusters:
            print(f"聚类键: {cluster[0]}")
            print(f"代表身份证: {cluster[1]}")
            print(f"代表姓名: {cluster[2]}")
            print(f"案件数量: {cluster[3]}")
            print(f"案件信息: {cluster[4]}")
            print("-" * 50)

def test_step_by_step():
    """逐步测试 SQL 的每个 CTE"""
    engine = create_test_database()

    with engine.connect() as conn:
        print("=== 逐步测试 SQL ===")

        # 测试 pivot_data
        print("\n1. pivot_data:")
        result = conn.execute(text("""
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
              AND qcf.entity_id > 0
            GROUP BY qcf.case_id, qcf.entity_id
            ORDER BY qcf.case_id, qcf.entity_id
        """))

        for row in result.fetchall():
            print(f"  {row}")

        # 测试 entities_with_multiple_cases
        print("\n2. entities_with_multiple_cases:")
        result = conn.execute(text("""
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
                  AND qcf.entity_id > 0
                GROUP BY qcf.case_id, qcf.entity_id
            )
            SELECT entity_id, ENTITY_NAME, ID_CARD, COUNT(DISTINCT case_id) as case_count
            FROM pivot_data
            WHERE ENTITY_NAME IS NOT NULL
            GROUP BY entity_id, ENTITY_NAME, ID_CARD
            HAVING COUNT(DISTINCT case_id) >= 2
            ORDER BY ENTITY_NAME
        """))

        for row in result.fetchall():
            print(f"  {row}")

if __name__ == "__main__":
    print("开始测试 cluster_entities SQL 逻辑...")
    test_step_by_step()
    test_current_sql()
